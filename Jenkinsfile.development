@Library('eks-shared-lib-jkt')_
pipeline {
    environment {
        RepoName = "${DeploymentName}-${environ}"
        environ = "development"
        DeploymentName = "pluang-spark-batch-processing-jobs"
        BucketName = "de-pluang-snapshot-resources-dev-jkt"
        GitUrl = "https://github.com/emasdigi/PluangSparkBatchProcessingJobs.git"
    }
    options {
        ansiColor('xterm')
        buildDiscarder logRotator(
            numToKeepStr: "5"
        )
        disableConcurrentBuilds abortPrevious: true
        preserveStashes()
        timestamps()
    }    
    agent {
        kubernetes {
            defaultContainer 'jnlp'
            yamlFile 'pluang-spark-batch-processing-jobs/agentpod.yaml'
        }
    }
    parameters {
        gitParameter (
            name: 'BRANCH_TAG',
            type: 'PT_BRANCH_TAG',
            branchFilter: 'origin/(.*)',
            defaultValue: 'development',
            quickFilterEnabled: true,
            useRepository: 'https://github.com/emasdigi/PluangSparkBatchProcessingJobs.git'
        )
    }
    stages {

        stage('build-and-upload') {
            steps {
                script {
                    container('aws') {
                        // Checkout the code
                        git branch: "${params.BRANCH_TAG}",
                            credentialsId: 'GitCreds',
                            url: "${GitUrl}"

                        // Upload main.py
                        sh "aws s3 cp main.py s3://${BucketName}/dev-pipeline/pluang_spark_batch_processing_jobs/main.py"

                        // Upload config folder recursively
                        sh "aws s3 cp config/ s3://${BucketName}/dev-pipeline/pluang_spark_batch_processing_jobs/config/ --recursive --exclude secret.json"

                        // Zip src folder
                        sh '''
                        find src -type f > file_list.txt
                        python3 -c "import os, zipfile; zip = zipfile.ZipFile('src.zip', 'w', zipfile.ZIP_DEFLATED); zip.write('src', 'src'); [zip.write(os.path.join(dp, f), os.path.join('src', os.path.relpath(os.path.join(dp, f), 'src')).replace(os.sep, '/')) for dp, dn, filenames in os.walk('src') for f in filenames if not f.startswith('._') and '.DS_Store' not in f]; zip.close()"
                        '''

                        // Upload zipped src
                        sh "aws s3 cp src.zip s3://${BucketName}/dev-pipeline/pluang_spark_batch_processing_jobs/src.zip"
                    }
                }
            }
        }

    }
    post {
        success {
            sendNotifications "SUCCESS", "${environ}", 'NO'
        }
        failure {
            sendNotifications "FAILED", "${environ}", 'NO'
        }
    }
}
